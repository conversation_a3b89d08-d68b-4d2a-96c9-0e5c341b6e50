/**
 * Utility functions for handling TON Connect errors in Telegram Mini Apps
 */

export interface TonConnectError {
  message: string;
  code?: number;
  name?: string;
}

/**
 * TON Connect error codes from the SDK
 */
export const TON_CONNECT_ERROR_CODES = {
  UNKNOWN_ERROR: 0,
  BAD_REQUEST_ERROR: 1,
  UNKNOWN_APP_ERROR: 100,
  USER_REJECTS_ERROR: 300,
  METHOD_NOT_SUPPORTED: 400,
} as const;

/**
 * Parse and format TON Connect errors for user-friendly display
 */
export function parseTonConnectError(error: any): string {
  if (!error) return 'Unknown error occurred';

  // Handle string errors
  if (typeof error === 'string') {
    return formatErrorMessage(error);
  }

  // Handle error objects
  const message = error.message || error.toString();
  const code = error.code;

  // Handle specific error codes
  if (code !== undefined) {
    switch (code) {
      case TON_CONNECT_ERROR_CODES.USER_REJECTS_ERROR:
        return 'Transaction was cancelled by user';
      case TON_CONNECT_ERROR_CODES.BAD_REQUEST_ERROR:
        return 'Invalid transaction request. Please try again.';
      case TON_CONNECT_ERROR_CODES.METHOD_NOT_SUPPORTED:
        return 'This operation is not supported by your wallet';
      case TON_CONNECT_ERROR_CODES.UNKNOWN_APP_ERROR:
        return 'Application error occurred. Please try again.';
      default:
        return formatErrorMessage(message);
    }
  }

  return formatErrorMessage(message);
}

/**
 * Format error messages for better user experience
 */
function formatErrorMessage(message: string): string {
  const lowerMessage = message.toLowerCase();

  // Common error patterns and their user-friendly messages
  if (
    lowerMessage.includes('user rejected') ||
    lowerMessage.includes('user denied')
  ) {
    return 'Transaction was cancelled by user';
  }

  if (
    lowerMessage.includes('insufficient funds') ||
    lowerMessage.includes('not enough')
  ) {
    return 'Insufficient funds in wallet';
  }

  if (lowerMessage.includes('network') || lowerMessage.includes('connection')) {
    return 'Network error. Please check your connection and try again.';
  }

  if (lowerMessage.includes('timeout') || lowerMessage.includes('timed out')) {
    return 'Transaction timed out. Please try again.';
  }

  if (lowerMessage.includes('wallet not connected')) {
    return 'Please connect your wallet first';
  }

  if (lowerMessage.includes('invalid address')) {
    return 'Invalid wallet address';
  }

  if (lowerMessage.includes('gas') || lowerMessage.includes('fee')) {
    return 'Transaction fee error. Please try again.';
  }

  // Return original message if no pattern matches, but clean it up
  return message.charAt(0).toUpperCase() + message.slice(1);
}

/**
 * Check if error is a user rejection
 */
export function isUserRejectionError(error: any): boolean {
  if (!error) return false;

  const message = (error.message || error.toString()).toLowerCase();
  const code = error.code;

  return (
    code === TON_CONNECT_ERROR_CODES.USER_REJECTS_ERROR ||
    message.includes('user rejected') ||
    message.includes('user denied') ||
    message.includes('cancelled')
  );
}

/**
 * Check if error is a network-related error
 */
export function isNetworkError(error: any): boolean {
  if (!error) return false;

  const message = (error.message || error.toString()).toLowerCase();

  return (
    message.includes('network') ||
    message.includes('connection') ||
    message.includes('timeout') ||
    message.includes('fetch')
  );
}

/**
 * Get transaction options optimized for Telegram Mini Apps
 */
export function getTMATransactionOptions() {
  return {
    modals: ['before', 'success', 'error'] as (
      | 'before'
      | 'success'
      | 'error'
    )[],
    notifications: ['before', 'success', 'error'] as (
      | 'before'
      | 'success'
      | 'error'
    )[],
    skipRedirectToWallet: 'ios' as const, // Recommended for iOS devices in TMA
  };
}

/**
 * Get return URL for Telegram Mini Apps
 */
export function getTMAReturnUrl(): string {
  const botUsername =
    process.env.NEXT_PUBLIC_TELEGRAM_BOT_USERNAME || 'mikepremdevbot';
  return `https://t.me/${botUsername}`;
}
