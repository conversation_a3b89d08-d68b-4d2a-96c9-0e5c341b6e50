'use client';

import { useTonConnectUI } from '@tonconnect/ui-react';
import { useEffect } from 'react';

import { getTMAReturnUrl } from '@/utils/ton-connect-error-utils';

/**
 * Hook to configure TON Connect UI for Telegram Mini Apps
 * Sets up proper return URLs and transaction options for TMA environment
 */
export function useTonConnectConfig() {
  const [tonConnectUI] = useTonConnectUI();

  useEffect(() => {
    // Check if we're in Telegram Mini App environment
    const isTMA = typeof window !== 'undefined' && window.Telegram?.WebApp;

    if (isTMA && tonConnectUI) {
      const returnUrl = getTMAReturnUrl();

      // Configure TMA-specific options
      tonConnectUI.uiOptions = {
        // Set return URL for Telegram Mini Apps
        twaReturnUrl: returnUrl,

        // Configure action behavior for better mobile experience
        actionsConfiguration: {
          // Skip redirect to wallet on iOS to prevent issues
          skipRedirectToWallet: 'ios',

          // Set return strategy for non-Telegram links
          returnStrategy: 'back',

          // Configure modals and notifications
          modals: ['before', 'success', 'error'] as (
            | 'before'
            | 'success'
            | 'error'
          )[],
          notifications: ['before', 'success', 'error'] as (
            | 'before'
            | 'success'
            | 'error'
          )[],
        },
      };

      console.log(
        'TON Connect UI configured for TMA with return URL:',
        returnUrl,
      );
    }
  }, [tonConnectUI]);

  return { tonConnectUI };
}
