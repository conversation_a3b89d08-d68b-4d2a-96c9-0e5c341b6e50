# Bot Configuration
BOT_TOKEN=**********:AAGtN-RtSQLRdFsQIvTVJWuZ6dRnsV6Do3A
WEB_APP_URL=https://marketplace-bltj7dyei.vercel.app/
# WEBHOOK_URL=https://nodejs-telegram-bot-dev-1064526164290.us-central1.run.app

# Server Configuration
PORT=8080
LOG_LEVEL=debug

# Firebase Configuration (marketplace-dev project)
FIREBASE_PROJECT_ID=marketplace-dev-76a4a
FIREBASE_REGION=us-central1
CLOUD_RUN_BASE_URL=4mofuqohoa-uc.a.run.app

# Google Cloud Configuration
GCLOUD_PROJECT=marketplace-dev-76a4a
ENABLE_CLOUD_LOGGING=true

# Health Check Authentication
HEALTH_CHECK_BEARER_TOKEN=f7dfe108632c48755d1bc61a4cd3beb3a14d55faec3f4eba95edd29f216ee1ec

# Redis Configuration
REDIS_HOST_LOCAL=127.0.0.1
REDIS_PORT_LOCAL=6379


GIFT_SIMULATION=true